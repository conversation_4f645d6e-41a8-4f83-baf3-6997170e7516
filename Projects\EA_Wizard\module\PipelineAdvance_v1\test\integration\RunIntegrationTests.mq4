//+------------------------------------------------------------------+
//|                                         RunIntegrationTests.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestRunnerWithDocuments.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("\n" + "=".StringRepeat(70));
    Print("  PipelineAdvance_v1 整合測試套件");
    Print("=".StringRepeat(70));
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print("測試類型: 整合測試");
    Print("=".StringRepeat(70));
    
    // 運行整合測試並生成文檔
    RunIntegrationTestsWithDocuments();
    
    Print("\n" + "=".StringRepeat(70));
    Print("  PipelineAdvance_v1 整合測試套件完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print("=".StringRepeat(70));
}

//+------------------------------------------------------------------+
//| 運行整合測試並生成文檔                                             |
//+------------------------------------------------------------------+
void RunIntegrationTestsWithDocuments()
{
    Print("📄 開始執行 PipelineAdvance_v1 整合測試並生成文檔...");
    
    // 創建測試運行器（生成文檔，顯示前10個結果）
    PipelineAdvanceV1TestRunnerWithDocs* runner = new PipelineAdvanceV1TestRunnerWithDocs(true, 10);
    
    // 運行整合測試
    runner.RunIntegrationTests();
    
    // 清理
    delete runner;
    
    Print("✅ 整合測試執行完成，文檔已生成");
}

//+------------------------------------------------------------------+
//| 運行整合測試（無文檔輸出）                                         |
//+------------------------------------------------------------------+
void RunIntegrationTestsOnly()
{
    Print("🚀 開始執行 PipelineAdvance_v1 整合測試（無文檔輸出）...");
    
    // 創建測試運行器（不生成文檔）
    PipelineAdvanceV1TestRunnerWithDocs* runner = new PipelineAdvanceV1TestRunnerWithDocs(false);
    
    // 運行整合測試
    runner.RunIntegrationTests();
    
    // 清理
    delete runner;
    
    Print("✅ 整合測試執行完成");
}

//+------------------------------------------------------------------+
//| 運行整合測試（無限制顯示）                                         |
//+------------------------------------------------------------------+
void RunIntegrationTestsWithUnlimitedDisplay()
{
    Print("📄 開始執行 PipelineAdvance_v1 整合測試（無限制顯示）...");
    
    // 創建測試運行器（生成文檔，無限制顯示）
    PipelineAdvanceV1TestRunnerWithDocs* runner = new PipelineAdvanceV1TestRunnerWithDocs(true, -1);
    
    // 運行整合測試
    runner.RunIntegrationTests();
    
    // 清理
    delete runner;
    
    Print("✅ 整合測試執行完成，完整文檔已生成");
}

//+------------------------------------------------------------------+
//| 運行整合測試（自定義顯示限制）                                     |
//+------------------------------------------------------------------+
void RunIntegrationTestsWithCustomLimit(int displayLimit)
{
    Print("📄 開始執行 PipelineAdvance_v1 整合測試（顯示限制: " + IntegerToString(displayLimit) + "）...");
    
    // 創建測試運行器（生成文檔，自定義顯示限制）
    PipelineAdvanceV1TestRunnerWithDocs* runner = new PipelineAdvanceV1TestRunnerWithDocs(true, displayLimit);
    
    // 運行整合測試
    runner.RunIntegrationTests();
    
    // 清理
    delete runner;
    
    Print("✅ 整合測試執行完成，文檔已生成");
}

//+------------------------------------------------------------------+
//| 快速整合測試檢查                                                   |
//+------------------------------------------------------------------+
bool QuickIntegrationCheck()
{
    Print("⚡ 開始快速整合測試檢查...");
    
    // 創建測試運行器（不生成文檔）
    PipelineAdvanceV1TestRunnerWithDocs* runner = new PipelineAdvanceV1TestRunnerWithDocs(false);
    
    // 運行整合測試
    runner.RunIntegrationTests();
    
    // 檢查結果
    bool allPassed = runner.AllTestsPassed();
    
    // 清理
    delete runner;
    
    if(allPassed)
    {
        Print("✅ 快速整合測試檢查通過");
    }
    else
    {
        Print("❌ 快速整合測試檢查失敗");
    }
    
    return allPassed;
}

//+------------------------------------------------------------------+
//| 字符串重複函數                                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result += str;
    }
    return result;
}
