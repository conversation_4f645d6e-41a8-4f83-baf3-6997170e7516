//+------------------------------------------------------------------+
//|                                      TestRunnerWithDocuments.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "TestPipelineAdvanceV1Integration.mqh"

//+------------------------------------------------------------------+
//| 測試文檔生成器 - 生成測試結果文檔                                  |
//+------------------------------------------------------------------+
class TestDocumentGenerator
{
private:
    string m_fileName;           // 文檔文件名
    int m_fileHandle;           // 文件句柄
    bool m_isOpen;              // 文件是否打開
    int m_displayLimit;         // 顯示限制
    
public:
    // 構造函數
    TestDocumentGenerator(string fileName = "PipelineAdvanceV1_Integration_Test_Report.txt", int displayLimit = 10)
        : m_fileName(fileName), m_fileHandle(-1), m_isOpen(false), m_displayLimit(displayLimit) {}
    
    // 析構函數
    ~TestDocumentGenerator()
    {
        CloseDocument();
    }
    
    // 開始文檔
    bool StartDocument(string title = "PipelineAdvance_v1 整合測試報告")
    {
        m_fileHandle = FileOpen(m_fileName, FILE_WRITE | FILE_TXT);
        if(m_fileHandle == INVALID_HANDLE)
        {
            Print("錯誤：無法創建測試文檔文件: " + m_fileName);
            return false;
        }
        
        m_isOpen = true;
        
        // 寫入文檔標題
        WriteHeader(title);
        return true;
    }
    
    // 關閉文檔
    void CloseDocument()
    {
        if(m_isOpen && m_fileHandle != INVALID_HANDLE)
        {
            WriteFooter();
            FileClose(m_fileHandle);
            m_isOpen = false;
            m_fileHandle = -1;
            Print("測試文檔已生成: " + m_fileName);
        }
    }
    
    // 寫入測試類別開始
    void WriteTestClassStart(string className)
    {
        if(!m_isOpen) return;
        
        FileWrite(m_fileHandle, "");
        FileWrite(m_fileHandle, "=".StringRepeat(60));
        FileWrite(m_fileHandle, "測試類別: " + className);
        FileWrite(m_fileHandle, "=".StringRepeat(60));
        FileWrite(m_fileHandle, "開始時間: " + TimeToString(TimeCurrent()));
        FileWrite(m_fileHandle, "");
    }
    
    // 寫入測試類別結束
    void WriteTestClassEnd(string className, int totalTests, int passedTests, int failedTests)
    {
        if(!m_isOpen) return;
        
        FileWrite(m_fileHandle, "");
        FileWrite(m_fileHandle, "-".StringRepeat(40));
        FileWrite(m_fileHandle, className + " 測試摘要:");
        FileWrite(m_fileHandle, "總測試數: " + IntegerToString(totalTests));
        FileWrite(m_fileHandle, "通過: " + IntegerToString(passedTests));
        FileWrite(m_fileHandle, "失敗: " + IntegerToString(failedTests));
        
        double successRate = totalTests > 0 ? (double)passedTests / totalTests * 100.0 : 0.0;
        FileWrite(m_fileHandle, "成功率: " + DoubleToString(successRate, 2) + "%");
        FileWrite(m_fileHandle, "結束時間: " + TimeToString(TimeCurrent()));
        FileWrite(m_fileHandle, "-".StringRepeat(40));
    }
    
    // 寫入測試結果
    void WriteTestResult(string testName, bool passed, string message = "")
    {
        if(!m_isOpen) return;
        
        string status = passed ? "[通過]" : "[失敗]";
        string line = status + " " + testName;
        
        if(!passed && message != "")
        {
            line += " - " + message;
        }
        
        FileWrite(m_fileHandle, line);
    }
    
    // 寫入性能信息
    void WritePerformanceInfo(string info)
    {
        if(!m_isOpen) return;
        
        FileWrite(m_fileHandle, "");
        FileWrite(m_fileHandle, "[性能信息] " + info);
    }
    
    // 設置顯示限制
    void SetDisplayLimit(int limit) { m_displayLimit = limit; }
    
    // 獲取顯示限制
    int GetDisplayLimit() const { return m_displayLimit; }

private:
    // 寫入文檔標題
    void WriteHeader(string title)
    {
        if(!m_isOpen) return;
        
        FileWrite(m_fileHandle, "=".StringRepeat(80));
        FileWrite(m_fileHandle, title);
        FileWrite(m_fileHandle, "=".StringRepeat(80));
        FileWrite(m_fileHandle, "生成時間: " + TimeToString(TimeCurrent()));
        FileWrite(m_fileHandle, "測試模組: PipelineAdvance_v1");
        FileWrite(m_fileHandle, "測試類型: 整合測試");
        FileWrite(m_fileHandle, "顯示限制: " + (m_displayLimit == -1 ? "無限制" : IntegerToString(m_displayLimit)));
        FileWrite(m_fileHandle, "=".StringRepeat(80));
    }
    
    // 寫入文檔結尾
    void WriteFooter()
    {
        if(!m_isOpen) return;
        
        FileWrite(m_fileHandle, "");
        FileWrite(m_fileHandle, "=".StringRepeat(80));
        FileWrite(m_fileHandle, "測試報告結束");
        FileWrite(m_fileHandle, "生成時間: " + TimeToString(TimeCurrent()));
        FileWrite(m_fileHandle, "=".StringRepeat(80));
    }
};

//+------------------------------------------------------------------+
//| 增強版測試運行器 - 支持文檔輸出                                    |
//+------------------------------------------------------------------+
class PipelineAdvanceV1TestRunnerWithDocs : public TestRunner
{
private:
    TestDocumentGenerator* m_docGenerator;  // 文檔生成器
    bool m_generateDocs;                    // 是否生成文檔
    int m_currentTestCount;                 // 當前測試計數
    int m_currentPassedCount;               // 當前通過計數
    int m_currentFailedCount;               // 當前失敗計數
    
public:
    // 構造函數
    PipelineAdvanceV1TestRunnerWithDocs(bool generateDocs = true, int displayLimit = 10)
        : TestRunner(), m_generateDocs(generateDocs), m_currentTestCount(0), 
          m_currentPassedCount(0), m_currentFailedCount(0)
    {
        if(m_generateDocs)
        {
            m_docGenerator = new TestDocumentGenerator("PipelineAdvanceV1_Integration_Test_Report.txt", displayLimit);
        }
        else
        {
            m_docGenerator = NULL;
        }
    }
    
    // 析構函數
    ~PipelineAdvanceV1TestRunnerWithDocs()
    {
        if(m_docGenerator != NULL)
        {
            delete m_docGenerator;
            m_docGenerator = NULL;
        }
    }
    
    // 運行整合測試
    void RunIntegrationTests()
    {
        Print("🚀 開始執行 PipelineAdvance_v1 整合測試...");
        
        if(m_generateDocs && m_docGenerator != NULL)
        {
            m_docGenerator.StartDocument("PipelineAdvance_v1 整合測試報告");
        }
        
        // 重置計數器
        ResetCounters();
        
        // 運行整合測試
        TestPipelineAdvanceV1Integration* integrationTest = new TestPipelineAdvanceV1Integration(this);
        RunTestCaseWithDocs(integrationTest);
        delete integrationTest;
        
        // 顯示總體摘要
        ShowFinalSummary();
        
        if(m_generateDocs && m_docGenerator != NULL)
        {
            m_docGenerator.CloseDocument();
        }
        
        Print("✅ PipelineAdvance_v1 整合測試執行完成");
    }
    
    // 運行測試類別並生成文檔
    void RunTestCaseWithDocs(TestCase* testCase)
    {
        if(testCase == NULL) return;
        
        string className = testCase.GetClassName();
        
        Print(StringFormat("=== 開始執行測試類別: %s ===", className));
        
        if(m_generateDocs && m_docGenerator != NULL)
        {
            m_docGenerator.WriteTestClassStart(className);
        }
        
        // 記錄開始時的計數
        int startTotalTests = GetTotalTests();
        int startPassedTests = GetPassedTests();
        int startFailedTests = GetFailedTests();
        
        // 執行測試
        testCase.RunTests();
        
        // 計算這個測試類別的統計
        int classTotalTests = GetTotalTests() - startTotalTests;
        int classPassedTests = GetPassedTests() - startPassedTests;
        int classFailedTests = GetFailedTests() - startFailedTests;
        
        if(m_generateDocs && m_docGenerator != NULL)
        {
            m_docGenerator.WriteTestClassEnd(className, classTotalTests, classPassedTests, classFailedTests);
        }
        
        Print(StringFormat("=== 完成測試類別: %s ===", className));
    }
    
    // 重寫記錄結果方法以支持文檔輸出
    void RecordResult(TestResult* result) override
    {
        if(result == NULL) return;
        
        // 調用父類方法
        TestRunner::RecordResult(result);
        
        // 生成文檔
        if(m_generateDocs && m_docGenerator != NULL)
        {
            m_docGenerator.WriteTestResult(result.GetTestName(), result.IsPassed(), result.GetMessage());
        }
    }
    
    // 記錄性能信息
    void RecordPerformanceInfo(string info)
    {
        Print("[性能信息] " + info);
        
        if(m_generateDocs && m_docGenerator != NULL)
        {
            m_docGenerator.WritePerformanceInfo(info);
        }
    }
    
    // 顯示最終摘要
    void ShowFinalSummary()
    {
        Print("\n" + "=".StringRepeat(50));
        Print("PipelineAdvance_v1 整合測試最終摘要");
        Print("=".StringRepeat(50));
        ShowSummary();
        Print("=".StringRepeat(50));
    }

private:
    // 重置計數器
    void ResetCounters()
    {
        m_currentTestCount = 0;
        m_currentPassedCount = 0;
        m_currentFailedCount = 0;
    }
};

//+------------------------------------------------------------------+
//| 字符串重複函數 - MQL4 輔助函數                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result += str;
    }
    return result;
}
