//+------------------------------------------------------------------+
//|                           TestPipelineAdvanceV1Integration.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "MockTradingPipeline.mqh"
#include "../../CompositePipeline.mqh"
#include "../../PipelineGroup.mqh"

//+------------------------------------------------------------------+
//| PipelineAdvance_v1 整合測試類別                                   |
//+------------------------------------------------------------------+
class TestPipelineAdvanceV1Integration : public TestCase
{
private:
    TestRunner* m_runner;        // 測試運行器引用

public:
    // 構造函數
    TestPipelineAdvanceV1Integration(TestRunner* runner)
        : TestCase("TestPipelineAdvanceV1Integration"), m_runner(runner) {}

    // 運行所有測試
    void RunTests() override
    {
        TestBasicWorkflow();
        TestComplexNestedStructure();
        TestErrorHandling();
        TestMixedResults();
        TestLargeScaleScenario();
        TestEventTypeHandling();
        TestEdgeCases();
        TestPerformanceScenario();
    }

private:
    // 測試基本工作流程
    void TestBasicWorkflow()
    {
        SetUp();

        Print("=== 測試基本工作流程 ===");

        // 創建基本流水線
        MockTradingPipeline* dataFeed = MockTradingPipelineFactory::CreateDataFeedPipeline();
        MockTradingPipeline* signal = MockTradingPipelineFactory::CreateSignalPipeline();
        MockTradingPipeline* order = MockTradingPipelineFactory::CreateOrderPipeline();

        // 創建複合流水線
        CompositePipeline* mainPipeline = new CompositePipeline("主工作流程");
        mainPipeline.AddPipeline(dataFeed);
        mainPipeline.AddPipeline(signal);
        mainPipeline.AddPipeline(order);

        // 創建流水線組
        PipelineGroup* tickGroup = new PipelineGroup("Tick處理組", "處理每個Tick的流水線組", TRADING_TICK);
        tickGroup.AddPipeline(mainPipeline);

        // 執行工作流程
        tickGroup.ExecuteAll();

        // 驗證結果
        m_runner.RecordResult(Assert::AssertTrue("基本工作流程_組執行成功",
            tickGroup.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("基本工作流程_主流水線執行",
            mainPipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("基本工作流程_數據饋送執行",
            dataFeed.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("基本工作流程_信號分析執行",
            signal.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("基本工作流程_訂單處理執行",
            order.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("基本工作流程_流水線數量",
            3, mainPipeline.GetPipelineCount()));

        // 清理
        delete tickGroup;
        delete mainPipeline;
        delete dataFeed;
        delete signal;
        delete order;

        TearDown();
    }

    // 測試複雜嵌套結構
    void TestComplexNestedStructure()
    {
        SetUp();

        Print("=== 測試複雜嵌套結構 ===");

        // 創建多層嵌套結構
        CompositePipeline* level1 = new CompositePipeline("第一層");
        CompositePipeline* level2 = new CompositePipeline("第二層");
        CompositePipeline* level3 = new CompositePipeline("第三層");

        // 第三層添加基本流水線
        level3.AddPipeline(MockTradingPipelineFactory::CreateDataFeedPipeline("L3_數據"));
        level3.AddPipeline(MockTradingPipelineFactory::CreateSignalPipeline("L3_信號"));

        // 第二層添加第三層和其他流水線
        level2.AddPipeline(level3);
        level2.AddPipeline(MockTradingPipelineFactory::CreateRiskPipeline("L2_風險"));

        // 第一層添加第二層和其他流水線
        level1.AddPipeline(MockTradingPipelineFactory::CreateLogPipeline("L1_日誌"));
        level1.AddPipeline(level2);
        level1.AddPipeline(MockTradingPipelineFactory::CreateOrderPipeline("L1_訂單"));

        // 執行嵌套結構
        level1.Execute();

        // 驗證嵌套執行
        m_runner.RecordResult(Assert::AssertTrue("嵌套結構_第一層執行",
            level1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("嵌套結構_第二層執行",
            level2.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("嵌套結構_第三層執行",
            level3.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("嵌套結構_第一層子數量",
            3, level1.GetPipelineCount()));
        m_runner.RecordResult(Assert::AssertEquals("嵌套結構_第二層子數量",
            2, level2.GetPipelineCount()));
        m_runner.RecordResult(Assert::AssertEquals("嵌套結構_第三層子數量",
            2, level3.GetPipelineCount()));

        // 清理
        delete level1;
        delete level2;
        delete level3;

        TearDown();
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        SetUp();

        Print("=== 測試錯誤處理 ===");

        // 創建包含失敗流水線的複合流水線
        CompositePipeline* errorPipeline = new CompositePipeline("錯誤處理測試");

        MockTradingPipeline* success1 = MockTradingPipelineFactory::CreateSuccessfulPipeline("成功1");
        MockTradingPipeline* failed = MockTradingPipelineFactory::CreateFailedPipeline("失敗");
        MockTradingPipeline* success2 = MockTradingPipelineFactory::CreateSuccessfulPipeline("成功2");

        errorPipeline.AddPipeline(success1);
        errorPipeline.AddPipeline(failed);
        errorPipeline.AddPipeline(success2);

        // 執行包含錯誤的流水線
        errorPipeline.Execute();

        // 驗證錯誤處理
        m_runner.RecordResult(Assert::AssertTrue("錯誤處理_複合流水線執行",
            errorPipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("錯誤處理_成功1執行",
            success1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("錯誤處理_失敗流水線執行",
            failed.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("錯誤處理_成功2執行",
            success2.IsExecuted()));

        // 測試異常流水線
        MockTradingPipeline* exception = MockTradingPipelineFactory::CreateExceptionPipeline("異常");
        CompositePipeline* exceptionPipeline = new CompositePipeline("異常測試");
        exceptionPipeline.AddPipeline(exception);

        exceptionPipeline.Execute();

        m_runner.RecordResult(Assert::AssertTrue("錯誤處理_異常流水線執行",
            exception.IsExecuted()));

        // 清理
        delete errorPipeline;
        delete success1;
        delete failed;
        delete success2;
        delete exceptionPipeline;
        delete exception;

        TearDown();
    }

    // 測試混合結果
    void TestMixedResults()
    {
        SetUp();

        Print("=== 測試混合結果 ===");

        // 創建混合結果的流水線組
        PipelineGroup* mixedGroup = new PipelineGroup("混合結果組", "包含成功和失敗的流水線組", TRADING_TICK);

        CompositePipeline* successPipeline = new CompositePipeline("成功複合");
        successPipeline.AddPipeline(MockTradingPipelineFactory::CreateSuccessfulPipeline("成功A"));
        successPipeline.AddPipeline(MockTradingPipelineFactory::CreateSuccessfulPipeline("成功B"));

        CompositePipeline* mixedPipeline = new CompositePipeline("混合複合");
        mixedPipeline.AddPipeline(MockTradingPipelineFactory::CreateSuccessfulPipeline("混合成功"));
        mixedPipeline.AddPipeline(MockTradingPipelineFactory::CreateFailedPipeline("混合失敗"));

        mixedGroup.AddPipeline(successPipeline);
        mixedGroup.AddPipeline(mixedPipeline);

        // 執行混合結果組
        mixedGroup.ExecuteAll();

        // 驗證混合結果
        m_runner.RecordResult(Assert::AssertTrue("混合結果_組執行完成",
            mixedGroup.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("混合結果_成功複合執行",
            successPipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("混合結果_混合複合執行",
            mixedPipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("混合結果_組內流水線數量",
            2, mixedGroup.GetPipelineCount()));

        // 清理
        delete mixedGroup;
        delete successPipeline;
        delete mixedPipeline;

        TearDown();
    }

    // 測試大規模場景
    void TestLargeScaleScenario()
    {
        SetUp();

        Print("=== 測試大規模場景 ===");

        // 創建大規模流水線組
        PipelineGroup* largeGroup = new PipelineGroup("大規模組", "包含大量流水線的組", TRADING_TICK);

        // 創建多個複合流水線
        const int COMPOSITE_COUNT = 5;
        const int PIPELINES_PER_COMPOSITE = 10;

        CompositePipeline* composites[COMPOSITE_COUNT];

        for(int i = 0; i < COMPOSITE_COUNT; i++)
        {
            string compositeName = StringFormat("大規模複合_%d", i + 1);
            composites[i] = new CompositePipeline(compositeName);

            // 每個複合流水線添加多個子流水線
            for(int j = 0; j < PIPELINES_PER_COMPOSITE; j++)
            {
                string pipelineName = StringFormat("大規模流水線_%d_%d", i + 1, j + 1);
                MockTradingPipeline* pipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline(pipelineName, 5);
                composites[i].AddPipeline(pipeline);
            }

            largeGroup.AddPipeline(composites[i]);
        }

        // 執行大規模場景
        datetime startTime = TimeCurrent();
        largeGroup.ExecuteAll();
        datetime endTime = TimeCurrent();

        // 驗證大規模執行
        m_runner.RecordResult(Assert::AssertTrue("大規模場景_組執行完成",
            largeGroup.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("大規模場景_複合流水線數量",
            COMPOSITE_COUNT, largeGroup.GetPipelineCount()));

        // 驗證每個複合流水線
        for(int i = 0; i < COMPOSITE_COUNT; i++)
        {
            string testName = StringFormat("大規模場景_複合%d執行", i + 1);
            m_runner.RecordResult(Assert::AssertTrue(testName, composites[i].IsExecuted()));

            string countTestName = StringFormat("大規模場景_複合%d子數量", i + 1);
            m_runner.RecordResult(Assert::AssertEquals(countTestName,
                PIPELINES_PER_COMPOSITE, composites[i].GetPipelineCount()));
        }

        Print(StringFormat("大規模場景執行時間: %d 秒", endTime - startTime));

        // 清理
        delete largeGroup;
        for(int i = 0; i < COMPOSITE_COUNT; i++)
        {
            delete composites[i];
        }

        TearDown();
    }

    // 測試事件類型處理
    void TestEventTypeHandling()
    {
        SetUp();

        Print("=== 測試事件類型處理 ===");

        // 測試不同事件類型的流水線組
        PipelineGroup* initGroup = new PipelineGroup("初始化組", "處理初始化事件", TRADING_INIT);
        PipelineGroup* tickGroup = new PipelineGroup("Tick組", "處理Tick事件", TRADING_TICK);
        PipelineGroup* deinitGroup = new PipelineGroup("清理組", "處理清理事件", TRADING_DEINIT);

        // 為每個組添加適當的流水線
        initGroup.AddPipeline(new CompositePipeline("初始化流水線"));
        tickGroup.AddPipeline(new CompositePipeline("Tick流水線"));
        deinitGroup.AddPipeline(new CompositePipeline("清理流水線"));

        // 執行不同事件類型
        initGroup.ExecuteAll();
        tickGroup.ExecuteAll();
        deinitGroup.ExecuteAll();

        // 驗證事件類型處理
        m_runner.RecordResult(Assert::AssertTrue("事件類型_初始化組執行",
            initGroup.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("事件類型_Tick組執行",
            tickGroup.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("事件類型_清理組執行",
            deinitGroup.IsExecuted()));

        // 清理
        delete initGroup;
        delete tickGroup;
        delete deinitGroup;

        TearDown();
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        SetUp();

        Print("=== 測試邊界情況 ===");

        // 測試空流水線組
        PipelineGroup* emptyGroup = new PipelineGroup("空組", "沒有流水線的組", TRADING_TICK);
        emptyGroup.ExecuteAll();
        m_runner.RecordResult(Assert::AssertTrue("邊界情況_空組執行",
            emptyGroup.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("邊界情況_空組流水線數量",
            0, emptyGroup.GetPipelineCount()));

        // 測試空複合流水線
        CompositePipeline* emptyComposite = new CompositePipeline("空複合");
        emptyComposite.Execute();
        m_runner.RecordResult(Assert::AssertTrue("邊界情況_空複合執行",
            emptyComposite.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("邊界情況_空複合子數量",
            0, emptyComposite.GetPipelineCount()));

        // 測試單個流水線的複合
        CompositePipeline* singleComposite = new CompositePipeline("單個複合");
        MockTradingPipeline* singlePipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("單獨");
        singleComposite.AddPipeline(singlePipeline);
        singleComposite.Execute();

        m_runner.RecordResult(Assert::AssertTrue("邊界情況_單個複合執行",
            singleComposite.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("邊界情況_單個子流水線執行",
            singlePipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("邊界情況_單個複合子數量",
            1, singleComposite.GetPipelineCount()));

        // 測試重複執行防護
        singleComposite.Execute(); // 再次執行
        m_runner.RecordResult(Assert::AssertTrue("邊界情況_重複執行防護",
            singleComposite.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("邊界情況_重複執行後執行次數",
            1, singlePipeline.GetExecutionCount()));

        // 測試重置後重新執行
        singleComposite.Restore();
        singlePipeline.Restore();
        singlePipeline.ResetExecutionCount();

        singleComposite.Execute();
        m_runner.RecordResult(Assert::AssertTrue("邊界情況_重置後重新執行",
            singleComposite.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("邊界情況_重置後執行次數",
            1, singlePipeline.GetExecutionCount()));

        // 清理
        delete emptyGroup;
        delete emptyComposite;
        delete singleComposite;
        delete singlePipeline;

        TearDown();
    }

    // 測試性能場景
    void TestPerformanceScenario()
    {
        SetUp();

        Print("=== 測試性能場景 ===");

        // 創建性能測試場景
        const int PERFORMANCE_PIPELINE_COUNT = 100;

        CompositePipeline* performancePipeline = new CompositePipeline("性能測試");
        MockTradingPipeline* pipelines[PERFORMANCE_PIPELINE_COUNT];

        // 創建大量快速執行的流水線
        for(int i = 0; i < PERFORMANCE_PIPELINE_COUNT; i++)
        {
            string name = StringFormat("性能流水線_%d", i + 1);
            pipelines[i] = MockTradingPipelineFactory::CreateSuccessfulPipeline(name, 1); // 1ms延遲
            performancePipeline.AddPipeline(pipelines[i]);
        }

        // 測量執行時間
        datetime startTime = GetTickCount();
        performancePipeline.Execute();
        datetime endTime = GetTickCount();

        int executionTime = (int)(endTime - startTime);

        // 驗證性能
        m_runner.RecordResult(Assert::AssertTrue("性能場景_大量流水線執行",
            performancePipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("性能場景_流水線數量",
            PERFORMANCE_PIPELINE_COUNT, performancePipeline.GetPipelineCount()));

        // 驗證所有流水線都已執行
        bool allExecuted = true;
        for(int i = 0; i < PERFORMANCE_PIPELINE_COUNT; i++)
        {
            if(!pipelines[i].IsExecuted())
            {
                allExecuted = false;
                break;
            }
        }
        m_runner.RecordResult(Assert::AssertTrue("性能場景_所有流水線執行", allExecuted));

        Print(StringFormat("性能測試執行時間: %d 毫秒", executionTime));
        Print(StringFormat("平均每個流水線執行時間: %.2f 毫秒",
              (double)executionTime / PERFORMANCE_PIPELINE_COUNT));

        // 清理
        delete performancePipeline;
        for(int i = 0; i < PERFORMANCE_PIPELINE_COUNT; i++)
        {
            delete pipelines[i];
        }

        TearDown();
    }

    // 設置方法
    void SetUp() override
    {
        Print("--- 設置測試環境 ---");
    }

    // 清理方法
    void TearDown() override
    {
        Print("--- 清理測試環境 ---");
    }
};
