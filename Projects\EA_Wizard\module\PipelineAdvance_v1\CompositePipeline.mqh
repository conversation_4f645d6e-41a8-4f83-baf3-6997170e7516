//+------------------------------------------------------------------+
//|                                           CompositePipeline.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "TradingPipeline.mqh"
#include "../mql4-lib-master/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| 複合流水線類                                                     |
//+------------------------------------------------------------------+
class CompositePipeline : public TradingPipeline
{
private:
    Vector<ITradingPipeline*> m_pipelines;  // 子流水線向量
    bool m_owned;                           // 是否擁有子流水線
    int m_maxPipelines;                      // 最大子流水線數量
    PipelineResult* m_last_result;           // 執行結果

public:
    // 構造函數
    CompositePipeline(string name, bool owned = false, int maxPipelines = 20)
        : TradingPipeline(name),
          m_pipelines(owned),
          m_maxPipelines(maxPipelines),
          m_last_result(new PipelineResult(false, "尚未執行", name, ERROR_LEVEL_INFO))
    {
    }

    // 析構函數
    virtual ~CompositePipeline()
    {
        Clear();
    }

    // 添加子流水線
    bool AddPipeline(ITradingPipeline* pipeline)
    {
        // 檢查子流水線是否為空或已達到最大數量限制
        if(pipeline == NULL || m_pipelines.size() >= m_maxPipelines)
        {
            m_last_result = new PipelineResult(false, "子流水線為空或已達到最大數量限制", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        if(!m_pipelines.add(pipeline))
        {
            m_last_result = new PipelineResult(false, "添加子流水線失敗", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        m_last_result = new PipelineResult(true, "成功添加子流水線", GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    // 移除子流水線
    bool RemovePipeline(ITradingPipeline* pipeline)
    {
        if(!m_pipelines.remove(pipeline))
        {
            m_last_result = new PipelineResult(false, "移除子流水線失敗", GetName(), ERROR_LEVEL_WARNING);
            return false;
        }

        m_last_result = new PipelineResult(true, "成功移除子流水線", GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    // 按名稱移除子流水線
    bool RemovePipelineByName(string name)
    {
        foreachv(ITradingPipeline*, pipeline, m_pipelines)
        {
            if(pipeline.GetName() == name)
            {
                m_last_result = new PipelineResult(true, "成功移除子流水線", GetName(), ERROR_LEVEL_INFO);
                return m_pipelines.remove(pipeline);
            }
        }

        m_last_result = new PipelineResult(false, "未找到名稱為 '" + name + "' 的子流水線", GetName(), ERROR_LEVEL_WARNING);
        return false;
    }

    // 清空所有子流水線
    void Clear()
    {
        m_pipelines.clear();
    }

    // 獲取子流水線數量
    int GetPipelineCount() const
    {
        return m_pipelines.size();
    }

    // 獲取最大子流水線數量
    int GetMaxPipelines() const
    {
        return m_maxPipelines;
    }

    // 按索引獲取子流水線
    ITradingPipeline* GetPipeline(int index, ITradingPipeline* parent)
    {
        if(index >= 0 && index < m_pipelines.size())
        {
            m_last_result = new PipelineResult(true, "成功獲取子流水線", GetName(), ERROR_LEVEL_INFO);
            return m_pipelines.get(index);
        }
        m_last_result = new PipelineResult(false, "索引超出範圍", GetName(), ERROR_LEVEL_WARNING);
        return parent;
    }

    // 按名稱查找子流水線
    ITradingPipeline* FindByName(string name, ITradingPipeline* parent)
    {
        foreachv(ITradingPipeline*, pipeline, m_pipelines)
        {
            if(pipeline.GetName() == name)
            {
                m_last_result = new PipelineResult(true, "成功找到子流水線", GetName(), ERROR_LEVEL_INFO);
                return pipeline;
            }
        }
        m_last_result = new PipelineResult(false, "未找到名稱為 '" + name + "' 的子流水線", GetName(), ERROR_LEVEL_WARNING);
        return parent;
    }

    // 重置所有子流水線
    void Restore() override
    {
        TradingPipeline::Restore();
        foreachv(ITradingPipeline*, pipeline, m_pipelines)
        {
            pipeline.Restore();
        }
    }

protected:
    // 實現具體的執行邏輯
    void Main() override
    {
        foreachv(ITradingPipeline*, pipeline, m_pipelines)
        {
            pipeline.Execute();
        }
    }
};
