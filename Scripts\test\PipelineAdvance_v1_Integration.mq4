//+------------------------------------------------------------------+
//|                                PipelineAdvance_v1_Integration.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../../Projects/EA_Wizard/module/PipelineAdvance_v1/test/integration/RunIntegrationTests.mq4"

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("\n" + "=".StringRepeat(80));
    Print("  PipelineAdvance_v1 整合測試演示腳本");
    Print("=".StringRepeat(80));
    Print("腳本位置: Scripts/test/PipelineAdvance_v1_Integration.mq4");
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print("=".StringRepeat(80));
    
    // 顯示可用的測試選項
    ShowTestOptions();
    
    // 運行默認的整合測試（生成文檔）
    Print("\n🚀 開始運行默認整合測試...");
    RunIntegrationTestsWithDocuments();
    
    Print("\n" + "=".StringRepeat(80));
    Print("  PipelineAdvance_v1 整合測試演示完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print("=".StringRepeat(80));
}

//+------------------------------------------------------------------+
//| 顯示測試選項                                                       |
//+------------------------------------------------------------------+
void ShowTestOptions()
{
    Print("\n📋 可用的測試選項：");
    Print("1. RunIntegrationTestsWithDocuments() - 運行整合測試並生成文檔");
    Print("2. RunIntegrationTestsOnly() - 只運行整合測試（無文檔）");
    Print("3. RunIntegrationTestsWithUnlimitedDisplay() - 無限制顯示");
    Print("4. RunIntegrationTestsWithCustomLimit(n) - 自定義顯示限制");
    Print("5. QuickIntegrationCheck() - 快速檢查");
    Print("");
    Print("📄 測試報告將生成到: MQL4/Files/PipelineAdvanceV1_Integration_Test_Report.txt");
    Print("");
}

//+------------------------------------------------------------------+
//| 字符串重複函數                                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result += str;
    }
    return result;
}
