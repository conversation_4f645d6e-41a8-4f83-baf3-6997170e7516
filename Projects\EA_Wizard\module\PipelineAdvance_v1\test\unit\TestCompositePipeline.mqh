//+------------------------------------------------------------------+
//|                                      TestCompositePipeline.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../CompositePipeline.mqh"
#include "../../TradingPipeline.mqh"

// 模擬 TradingPipeline 以便測試
class MockTradingPipeline : public TradingPipeline
{
public:
    bool executed;
    string lastError;

    MockTradingPipeline(string name) : TradingPipeline(name), executed(false), lastError("") {}

    // 重寫 Restore 方法以重置 executed 標誌
    virtual void Restore() override
    {
        TradingPipeline::Restore(); // 調用基類的 Restore 方法
        executed = false;           // 重置自己的 executed 標誌
        lastError = "";             // 重置錯誤信息
    }

protected:
    virtual void Main() override
    {
        executed = true;
        // 模擬一些操作
    }
};

//+------------------------------------------------------------------+
//| TestCompositePipeline 測試類                                     |
//+------------------------------------------------------------------+
class TestCompositePipeline : public TestCase
{
private:
    TestRunner* m_runner;

public:
    TestCompositePipeline() : TestCase("TestCompositePipeline")
    {
        m_runner = new TestRunner();
    }

    ~TestCompositePipeline()
    {
        if(m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }

    // 運行所有測試
    void RunTests() override
    {
        TestAddPipeline();
        TestRemovePipeline();
        TestRemovePipelineByName();
        TestClearPipelines();
        TestGetPipelineCount();
        TestGetMaxPipelines();
        TestGetPipelineByIndex();
        TestFindPipelineByName();
        TestRestorePipelines();
        TestExecutePipelines();

        // 顯示測試摘要
        m_runner.ShowSummary();
    }

protected:
    // 測試 AddPipeline 方法
    void TestAddPipeline()
    {
        Print("\n--- 測試 AddPipeline ---");
        CompositePipeline* composite = new CompositePipeline("TestAdd");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        // 測試成功添加
        m_runner.RecordResult(Assert::AssertTrue("TestAddPipeline_Add1", composite.AddPipeline(pipeline1), "應能成功添加第一個流水線"));
        m_runner.RecordResult(Assert::AssertEquals("TestAddPipeline_Count1", 1, composite.GetPipelineCount(), "流水線數量應為 1"));

        // 測試添加第二個
        m_runner.RecordResult(Assert::AssertTrue("TestAddPipeline_Add2", composite.AddPipeline(pipeline2), "應能成功添加第二個流水線"));
        m_runner.RecordResult(Assert::AssertEquals("TestAddPipeline_Count2", 2, composite.GetPipelineCount(), "流水線數量應為 2"));

        // 測試添加空流水線
        m_runner.RecordResult(Assert::AssertFalse("TestAddPipeline_AddNull", composite.AddPipeline(NULL), "不應能添加空流水線"));

        // 測試超過最大數量限制
        CompositePipeline* limitedComposite = new CompositePipeline("LimitedTest", false, 1);
        MockTradingPipeline* limitedPipeline = new MockTradingPipeline("LimitedPipeline");
        m_runner.RecordResult(Assert::AssertTrue("TestAddPipeline_Limited", limitedComposite.AddPipeline(limitedPipeline), "應能添加一個流水線到有限容量的複合流水線"));
        m_runner.RecordResult(Assert::AssertFalse("TestAddPipeline_Exceed", limitedComposite.AddPipeline(pipeline1), "不應能添加超過最大數量的流水線"));

        delete composite;
        delete pipeline1;
        delete pipeline2;
        delete limitedComposite;
        delete limitedPipeline;
    }

    // 測試 RemovePipeline 方法
    void TestRemovePipeline()
    {
        Print("\n--- 測試 RemovePipeline ---");
        CompositePipeline* composite = new CompositePipeline("TestRemove");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        // 測試成功移除
        m_runner.RecordResult(Assert::AssertTrue("TestRemovePipeline_Remove", composite.RemovePipeline(pipeline1), "應能成功移除第一個流水線"));
        m_runner.RecordResult(Assert::AssertEquals("TestRemovePipeline_Count", 1, composite.GetPipelineCount(), "流水線數量應為 1"));

        // 測試移除不存在的流水線
        MockTradingPipeline* nonExistent = new MockTradingPipeline("NonExistent");
        m_runner.RecordResult(Assert::AssertFalse("TestRemovePipeline_NonExistent", composite.RemovePipeline(nonExistent), "不應能移除不存在的流水線"));

        // 測試移除空流水線
        m_runner.RecordResult(Assert::AssertFalse("TestRemovePipeline_Null", composite.RemovePipeline(NULL), "不應能移除空流水線"));

        delete composite;
        delete pipeline1;
        delete pipeline2;
        delete nonExistent;
    }

    // 測試 RemovePipelineByName 方法
    void TestRemovePipelineByName()
    {
        Print("\n--- 測試 RemovePipelineByName ---");
        CompositePipeline* composite = new CompositePipeline("TestRemoveByName");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        // 測試成功移除
        m_runner.RecordResult(Assert::AssertTrue("TestRemovePipelineByName_Remove", composite.RemovePipelineByName("Pipeline1"), "應能成功按名稱移除流水線"));
        m_runner.RecordResult(Assert::AssertEquals("TestRemovePipelineByName_Count", 1, composite.GetPipelineCount(), "流水線數量應為 1"));

        // 測試移除不存在的流水線
        m_runner.RecordResult(Assert::AssertFalse("TestRemovePipelineByName_NonExistent", composite.RemovePipelineByName("NonExistent"), "不應能按名稱移除不存在的流水線"));

        delete composite;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試 Clear 方法
    void TestClearPipelines()
    {
        Print("\n--- 測試 ClearPipelines ---");
        CompositePipeline* composite = new CompositePipeline("TestClear");
        composite.AddPipeline(new MockTradingPipeline("Pipeline1"));
        composite.AddPipeline(new MockTradingPipeline("Pipeline2"));

        composite.Clear();
        m_runner.RecordResult(Assert::AssertEquals("TestClearPipelines_Count", 0, composite.GetPipelineCount(), "流水線數量應為 0"));

        delete composite;
    }

    // 測試 GetPipelineCount 方法
    void TestGetPipelineCount()
    {
        Print("\n--- 測試 GetPipelineCount ---");
        CompositePipeline* composite = new CompositePipeline("TestCount");
        m_runner.RecordResult(Assert::AssertEquals("TestGetPipelineCount_Initial", 0, composite.GetPipelineCount(), "初始流水線數量應為 0"));

        composite.AddPipeline(new MockTradingPipeline("Pipeline1"));
        m_runner.RecordResult(Assert::AssertEquals("TestGetPipelineCount_Add1", 1, composite.GetPipelineCount(), "添加後流水線數量應為 1"));

        composite.AddPipeline(new MockTradingPipeline("Pipeline2"));
        m_runner.RecordResult(Assert::AssertEquals("TestGetPipelineCount_Add2", 2, composite.GetPipelineCount(), "添加後流水線數量應為 2"));

        composite.RemovePipelineByName("Pipeline1");
        m_runner.RecordResult(Assert::AssertEquals("TestGetPipelineCount_Remove", 1, composite.GetPipelineCount(), "移除後流水線數量應為 1"));

        delete composite;
    }

    // 測試 GetMaxPipelines 方法
    void TestGetMaxPipelines()
    {
        Print("\n--- 測試 GetMaxPipelines ---");
        CompositePipeline* compositeDefault = new CompositePipeline("TestMaxDefault");
        m_runner.RecordResult(Assert::AssertEquals("TestGetMaxPipelines_Default", 20, compositeDefault.GetMaxPipelines(), "默認最大流水線數量應為 20"));

        CompositePipeline* compositeCustom = new CompositePipeline("TestMaxCustom", false, 5);
        m_runner.RecordResult(Assert::AssertEquals("TestGetMaxPipelines_Custom", 5, compositeCustom.GetMaxPipelines(), "自定義最大流水線數量應為 5"));

        delete compositeDefault;
        delete compositeCustom;
    }

    // 測試 GetPipeline 方法 (按索引)
    void TestGetPipelineByIndex()
    {
        Print("\n--- 測試 GetPipelineByIndex ---");
        CompositePipeline* composite = new CompositePipeline("TestGetByIndex");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        // 測試有效索引
        ITradingPipeline* retrievedPipeline = composite.GetPipeline(0, NULL);
        m_runner.RecordResult(Assert::AssertNotNull("TestGetPipelineByIndex_Get0", retrievedPipeline, "應能獲取索引 0 的流水線"));
        m_runner.RecordResult(Assert::AssertEquals("TestGetPipelineByIndex_Name0", "Pipeline1", retrievedPipeline.GetName(), "獲取的流水線名稱應為 Pipeline1"));

        retrievedPipeline = composite.GetPipeline(1, NULL);
        m_runner.RecordResult(Assert::AssertNotNull("TestGetPipelineByIndex_Get1", retrievedPipeline, "應能獲取索引 1 的流水線"));
        m_runner.RecordResult(Assert::AssertEquals("TestGetPipelineByIndex_Name1", "Pipeline2", retrievedPipeline.GetName(), "獲取的流水線名稱應為 Pipeline2"));

        // 測試無效索引 - 當索引無效時，方法返回 parent 參數（在這裡是 NULL）
        ITradingPipeline* invalidResult = composite.GetPipeline(2, NULL);
        m_runner.RecordResult(Assert::AssertNull("TestGetPipelineByIndex_Invalid2", invalidResult, "索引 2 超出範圍時應返回 parent (NULL)"));

        invalidResult = composite.GetPipeline(-1, NULL);
        m_runner.RecordResult(Assert::AssertNull("TestGetPipelineByIndex_InvalidNeg", invalidResult, "索引 -1 超出範圍時應返回 parent (NULL)"));

        delete composite;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試 FindByName 方法
    void TestFindPipelineByName()
    {
        Print("\n--- 測試 FindPipelineByName ---");
        CompositePipeline* composite = new CompositePipeline("TestFindByName");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        // 測試找到存在的流水線
        ITradingPipeline* foundPipeline = composite.FindByName("Pipeline1", NULL);
        m_runner.RecordResult(Assert::AssertNotNull("TestFindPipelineByName_Found", foundPipeline, "應能找到名稱為 Pipeline1 的流水線"));
        m_runner.RecordResult(Assert::AssertEquals("TestFindPipelineByName_Name", "Pipeline1", foundPipeline.GetName(), "找到的流水線名稱應為 Pipeline1"));

        // 測試找不到不存在的流水線 - 當找不到時，方法返回 parent 參數（在這裡是 NULL）
        ITradingPipeline* notFoundResult = composite.FindByName("NonExistent", NULL);
        m_runner.RecordResult(Assert::AssertNull("TestFindPipelineByName_NotFound", notFoundResult, "找不到名稱為 NonExistent 的流水線時應返回 parent (NULL)"));

        delete composite;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試 Restore 方法
    void TestRestorePipelines()
    {
        Print("\n--- 測試 RestorePipelines ---");
        CompositePipeline* composite = new CompositePipeline("TestRestore");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        composite.Execute(); // 執行以設置 executed 狀態
        m_runner.RecordResult(Assert::AssertTrue("TestRestorePipelines_Executed1", pipeline1.executed, "流水線1應已執行"));
        m_runner.RecordResult(Assert::AssertTrue("TestRestorePipelines_Executed2", pipeline2.executed, "流水線2應已執行"));

        composite.Restore();
        m_runner.RecordResult(Assert::AssertFalse("TestRestorePipelines_Restored1", pipeline1.executed, "流水線1應已重置"));
        m_runner.RecordResult(Assert::AssertFalse("TestRestorePipelines_Restored2", pipeline2.executed, "流水線2應已重置"));

        delete composite;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試 Execute 方法
    void TestExecutePipelines()
    {
        Print("\n--- 測試 ExecutePipelines ---");
        CompositePipeline* composite = new CompositePipeline("TestExecute");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        composite.Execute();
        m_runner.RecordResult(Assert::AssertTrue("TestExecutePipelines_Executed1", pipeline1.executed, "流水線1應已執行"));
        m_runner.RecordResult(Assert::AssertTrue("TestExecutePipelines_Executed2", pipeline2.executed, "流水線2應已執行"));

        // 再次執行，確認不會重複執行 Main
        pipeline1.executed = false; // 重置以便檢查
        pipeline2.executed = false;
        composite.Execute();
        m_runner.RecordResult(Assert::AssertFalse("TestExecutePipelines_NoReexec1", pipeline1.executed, "流水線1不應重複執行 Main"));
        m_runner.RecordResult(Assert::AssertFalse("TestExecutePipelines_NoReexec2", pipeline2.executed, "流水線2不應重複執行 Main"));

        delete composite;
        delete pipeline1;
        delete pipeline2;
    }


};
